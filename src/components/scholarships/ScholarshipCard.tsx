import React from 'react';
import { Link } from 'react-router-dom';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level?: string;
  country?: string;
  deadline: string;
  isOpen: boolean;
  thumbnail?: string;
}

interface ScholarshipCardProps {
  scholarship: Scholarship;
}

const ScholarshipCard: React.FC<ScholarshipCardProps> = ({ scholarship }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden hover:-translate-y-1 transition-transform duration-200">
      <div className="relative aspect-w-16 aspect-h-9">
        <img
          src={scholarship.thumbnail ? `http://localhost:5000${scholarship.thumbnail}` : '/assets/default-scholarship.jpg'}
          alt={scholarship.title}
          className="w-full h-48 object-contain"
          style={{ backgroundColor: '#f8f9fa' }}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/assets/default-scholarship.jpg';
          }}
        />

        {/* Country overlay */}
        {scholarship.country && (
          <div className="absolute bottom-0 left-0 bg-gradient-to-r from-black/70 to-transparent w-full py-2 px-3">
            <span className="text-white text-xs font-medium">{scholarship.country}</span>
          </div>
        )}
      </div>
      <div className="p-6">
        <div className="flex items-center justify-between">
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              scholarship.isOpen
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}
          >
            {scholarship.isOpen ? 'Open' : 'Closed'}
          </span>
          {scholarship.deadline && (
            <span className="text-sm text-gray-500">
              Deadline: {new Date(scholarship.deadline).toLocaleDateString()}
            </span>
          )}
        </div>
        <h3 className="mt-4 text-lg font-medium text-gray-900">
          {scholarship.title}
        </h3>
        <p className="mt-2 text-sm text-gray-500 line-clamp-2">
          {scholarship.description}
        </p>
        <div className="mt-4 flex flex-wrap gap-2">
          {scholarship.level && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              {scholarship.level}
            </span>
          )}
          {scholarship.country && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              {scholarship.country}
            </span>
          )}
        </div>
        <div className="mt-6">
          <Link
            to={`/scholarships/${scholarship.id}`}
            className="inline-flex items-center justify-center w-full px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            View Details
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ScholarshipCard;