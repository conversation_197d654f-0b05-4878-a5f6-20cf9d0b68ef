import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';

interface EnhancedHeroSectionProps {
  scrollToLatestScholarships: () => void;
}

const EnhancedHeroSection: React.FC<EnhancedHeroSectionProps> = ({
  scrollToLatestScholarships
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentBackground, setCurrentBackground] = useState(0);
  const { translations } = useLanguage();

  const backgrounds = [
    '/assets/hero-bg-1.jpg',
    '/assets/hero-bg-2.jpg',
    '/assets/hero-bg-3.jpg'
  ];

  // Fallback images if the assets don't exist
  const fallbackBackgrounds = [
    'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=1800',
    'https://images.unsplash.com/photo-1517486808906-6ca8b3f8e1c1?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=1800',
    'https://images.unsplash.com/photo-1523580846011-d3a5bc25702b?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=1800'
  ];

  useEffect(() => {
    setIsVisible(true);

    // Change background every 7 seconds
    const interval = setInterval(() => {
      setCurrentBackground(prev => (prev + 1) % backgrounds.length);
    }, 7000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-gray-900 via-primary-dark to-primary">
      {/* Animated background with overlay */}
      <div className="absolute inset-0 z-0">
        {backgrounds.map((bg, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1500 ease-in-out ${
              currentBackground === index ? 'opacity-30' : 'opacity-0'
            }`}
          >
            <img
              src={bg}
              alt={`Background ${index + 1}`}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = fallbackBackgrounds[index];
              }}
            />
          </div>
        ))}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary-dark/80 mix-blend-multiply" />
      </div>

      {/* Decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-white/5 blur-3xl"></div>
        <div className="absolute top-1/2 -left-24 w-80 h-80 rounded-full bg-white/5 blur-3xl"></div>
        <div className="absolute -bottom-32 left-1/2 transform -translate-x-1/2 w-2/3 h-48 bg-white/10 blur-3xl"></div>

        {/* Animated floating shapes */}
        <div className="absolute top-20 left-[10%] w-12 h-12 rounded-full bg-primary-light/20 animate-float"></div>
        <div className="absolute top-40 right-[15%] w-24 h-24 rounded-lg bg-primary-light/10 rotate-45 animate-float-delayed"></div>
        <div className="absolute bottom-20 left-[20%] w-16 h-16 rounded-lg bg-white/10 rotate-12 animate-float-slow"></div>
      </div>

      {/* Hero content */}
      <div className={`relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-4 md:pt-24 md:pb-6 lg:pt-28 lg:pb-8 transition-all duration-1000 ease-out transform ${
        isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
      }`}>
        <div className="flex flex-col lg:flex-row items-center">
          {/* Left column - Text content */}
          <div className="text-center lg:text-left lg:w-1/2 mb-4 lg:mb-0 lg:pr-6">


            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white leading-tight">
              <span className="block">{translations.home.hero.titlePart1}</span>
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-white via-blue-100 to-white">
                {translations.home.hero.titlePart2}
              </span>
            </h1>

            <p className="mt-2 text-sm text-white/90 leading-relaxed max-w-lg mx-auto lg:mx-0">
              {translations.home.hero.subtitle}
            </p>

            <div className="mt-3 flex flex-col sm:flex-row justify-center lg:justify-start gap-2">
              <button
                onClick={scrollToLatestScholarships}
                className="px-4 py-2 bg-white text-primary font-medium rounded-lg shadow-md hover:shadow-lg hover:bg-gray-50 transition-all duration-300 flex items-center justify-center group text-sm"
              >
                {translations.home.hero.exploreButton}
                <svg className="ml-1 h-3 w-3 transform group-hover:translate-y-1 transition-transform duration-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </button>

              <Link
                to="/scholarships"
                className="px-4 py-2 bg-transparent border border-white text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-300 flex items-center justify-center text-sm"
              >
                {translations.home.hero.allScholarshipsButton}
                <svg className="ml-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>

          </div>

          {/* Right column - Compact professional card */}
          <div className="lg:w-1/2 relative">
            <div className="relative mx-auto max-w-xs">
              {/* Card */}
              <div className="relative bg-white/10 backdrop-blur-md rounded-lg overflow-hidden border border-white/20 shadow-lg">
                <div className="p-3">
                  <div className="flex items-center mb-2">
                    <div className="w-6 h-6 rounded-full bg-primary flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <div className="ml-2">
                      <h3 className="text-sm font-semibold text-white">{translations.home.hero.findScholarship}</h3>
                      <p className="text-white/80 text-xs">{translations.home.hero.filterByLevel}</p>
                    </div>
                  </div>

                  <div className="space-y-1.5 mb-3">
                    {translations.scholarships.levels.map((level, index) => (
                      <Link
                        key={level.value}
                        to={`/scholarships?level=${level.value}`}
                        className="flex items-center p-1.5 rounded-md bg-white/10 hover:bg-white/20 transition-colors duration-300"
                      >
                        <div className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium ${
                          index === 0 ? 'bg-blue-500/20 text-blue-100' :
                          index === 1 ? 'bg-purple-500/20 text-purple-100' :
                          'bg-indigo-500/20 text-indigo-100'
                        }`}>
                          {index + 1}
                        </div>
                        <span className="ml-2 text-white font-medium text-xs">{level.label}</span>
                        <svg className="ml-auto h-3 w-3 text-white/70" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                      </Link>
                    ))}
                  </div>

                  <Link
                    to="/scholarships"
                    className="block w-full py-1.5 text-center bg-white/20 hover:bg-white/30 text-white font-medium rounded-md transition-colors duration-300 text-xs"
                  >
                    {translations.home.hero.viewAllScholarships}
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EnhancedHeroSection;
